"""
Rubik's Cube Data Structure and Move Engine
Supports NxN cubes with complete move implementation
"""

import copy
import random


class Cube:
    """
    Represents a Rubik's Cube of any size (NxN).
    Uses standard color scheme: White-Yellow, Green-Blue, Orange-Red
    Face notation: U(p), D(own), F(ront), B(ack), L(eft), R(ight)
    """
    
    # Standard color mapping
    COLORS = {
        'U': 'white',    # Up face
        'D': 'yellow',   # Down face  
        'F': 'green',    # Front face
        'B': 'blue',     # Back face
        'L': 'orange',   # Left face
        'R': 'red'       # Right face
    }
    
    # Color codes for display
    COLOR_CODES = {
        'white': 'W',
        'yellow': 'Y', 
        'green': 'G',
        'blue': 'B',
        'orange': 'O',
        'red': 'R'
    }
    
    def __init__(self, size=3):
        """Initialize a solved cube of given size."""
        self.size = size
        self.faces = {}
        self.reset()
    
    def reset(self):
        """Reset cube to solved state."""
        for face, color in self.COLORS.items():
            self.faces[face] = [[self.COLOR_CODES[color] for _ in range(self.size)] 
                               for _ in range(self.size)]
    
    def copy(self):
        """Create a deep copy of the cube."""
        new_cube = Cube(self.size)
        new_cube.faces = copy.deepcopy(self.faces)
        return new_cube
    
    def rotate_face_clockwise(self, face):
        """Rotate a face 90 degrees clockwise."""
        # Transpose and reverse each row
        self.faces[face] = [list(row) for row in zip(*self.faces[face][::-1])]
    
    def rotate_face_counter_clockwise(self, face):
        """Rotate a face 90 degrees counter-clockwise."""
        # Reverse each row and transpose
        self.faces[face] = [list(row) for row in zip(*self.faces[face])][::-1]
    
    def get_edge_slice(self, face, edge, layer=0):
        """Get a slice of stickers from an edge of a face."""
        if edge == 'top':
            return self.faces[face][layer]
        elif edge == 'bottom':
            return self.faces[face][self.size - 1 - layer]
        elif edge == 'left':
            return [self.faces[face][i][layer] for i in range(self.size)]
        elif edge == 'right':
            return [self.faces[face][i][self.size - 1 - layer] for i in range(self.size)]
    
    def set_edge_slice(self, face, edge, values, layer=0):
        """Set a slice of stickers on an edge of a face."""
        if edge == 'top':
            self.faces[face][layer] = values[:]
        elif edge == 'bottom':
            self.faces[face][self.size - 1 - layer] = values[:]
        elif edge == 'left':
            for i in range(self.size):
                self.faces[face][i][layer] = values[i]
        elif edge == 'right':
            for i in range(self.size):
                self.faces[face][i][self.size - 1 - layer] = values[i]
    
    def apply_move(self, move):
        """
        Apply a move to the cube.
        Supports: R, L, U, D, F, B and their variants (', 2)
        Also supports wide moves (Rw, Lw, etc.) and slice moves (M, E, S)
        """
        if not move:
            return
            
        # Parse move notation
        face = move[0]
        modifiers = move[1:]
        
        # Handle wide moves
        wide = 'w' in modifiers
        layers = 2 if wide else 1
        
        # Handle double moves
        double = '2' in modifiers
        times = 2 if double else 1
        
        # Handle counter-clockwise
        counter = "'" in modifiers
        if counter:
            times = 3 if times == 1 else 2  # 3 clockwise = 1 counter-clockwise
        
        # Apply the move the specified number of times
        for _ in range(times):
            if face in ['R', 'L', 'U', 'D', 'F', 'B']:
                self._apply_face_turn(face, layers)
            elif face == 'M':  # Middle slice (between L and R)
                self._apply_middle_slice()
            elif face == 'E':  # Equatorial slice (between U and D)
                self._apply_equatorial_slice()
            elif face == 'S':  # Standing slice (between F and B)
                self._apply_standing_slice()
    
    def _apply_face_turn(self, face, layers=1):
        """Apply a face turn affecting the specified number of layers."""
        # Rotate the face itself (only for outermost layer)
        if layers >= 1:
            self.rotate_face_clockwise(face)
        
        # Move adjacent edges
        for layer in range(layers):
            if face == 'R':
                self._cycle_edges([
                    ('U', 'right', layer),
                    ('F', 'right', layer), 
                    ('D', 'right', layer),
                    ('B', 'left', self.size - 1 - layer)
                ], reverse_last=True)
            elif face == 'L':
                self._cycle_edges([
                    ('U', 'left', layer),
                    ('B', 'right', self.size - 1 - layer),
                    ('D', 'left', layer),
                    ('F', 'left', layer)
                ], reverse_first=False, reverse_last=False, reverse_second=True)
            elif face == 'U':
                self._cycle_edges([
                    ('F', 'top', layer),
                    ('L', 'top', layer),
                    ('B', 'top', layer),
                    ('R', 'top', layer)
                ])
            elif face == 'D':
                self._cycle_edges([
                    ('F', 'bottom', layer),
                    ('R', 'bottom', layer),
                    ('B', 'bottom', layer),
                    ('L', 'bottom', layer)
                ])
            elif face == 'F':
                self._cycle_edges([
                    ('U', 'bottom', layer),
                    ('R', 'left', layer),
                    ('D', 'top', layer),
                    ('L', 'right', layer)
                ], reverse_third=True, reverse_first=False)
            elif face == 'B':
                self._cycle_edges([
                    ('U', 'top', layer),
                    ('L', 'left', layer),
                    ('D', 'bottom', layer),
                    ('R', 'right', layer)
                ], reverse_first=True, reverse_third=True)
    
    def _cycle_edges(self, edges, reverse_first=False, reverse_second=False, 
                    reverse_third=False, reverse_last=False):
        """Cycle edge slices in the specified order."""
        # Get all edge slices
        slices = []
        for face, edge, layer in edges:
            slice_data = self.get_edge_slice(face, edge, layer)
            slices.append(slice_data[:])
        
        # Apply reversals as needed
        if reverse_first:
            slices[0] = slices[0][::-1]
        if reverse_second:
            slices[1] = slices[1][::-1]
        if reverse_third:
            slices[2] = slices[2][::-1]
        if reverse_last:
            slices[3] = slices[3][::-1]
        
        # Cycle the slices
        temp = slices[0]
        for i in range(len(edges) - 1):
            self.set_edge_slice(edges[i][0], edges[i][1], slices[i + 1], edges[i][2])
        self.set_edge_slice(edges[-1][0], edges[-1][1], temp, edges[-1][2])
    
    def _apply_middle_slice(self):
        """Apply M slice move (like L but middle layer only)."""
        if self.size < 3:
            return
        layer = self.size // 2
        self._cycle_edges([
            ('U', 'left', layer),
            ('B', 'right', self.size - 1 - layer),
            ('D', 'left', layer),
            ('F', 'left', layer)
        ], reverse_second=True)
    
    def _apply_equatorial_slice(self):
        """Apply E slice move (like D but middle layer only)."""
        if self.size < 3:
            return
        layer = self.size // 2
        self._cycle_edges([
            ('F', 'bottom', layer),
            ('L', 'bottom', layer),
            ('B', 'bottom', layer),
            ('R', 'bottom', layer)
        ])
    
    def _apply_standing_slice(self):
        """Apply S slice move (like F but middle layer only)."""
        if self.size < 3:
            return
        layer = self.size // 2
        self._cycle_edges([
            ('U', 'bottom', layer),
            ('R', 'left', layer),
            ('D', 'top', layer),
            ('L', 'right', layer)
        ], reverse_third=True)
    
    def scramble(self, num_moves=25):
        """Apply random moves to scramble the cube."""
        moves = ['R', 'L', 'U', 'D', 'F', 'B']
        modifiers = ['', "'", '2']
        
        applied_moves = []
        for _ in range(num_moves):
            move = random.choice(moves) + random.choice(modifiers)
            self.apply_move(move)
            applied_moves.append(move)
        
        return applied_moves
    
    def is_solved(self):
        """Check if the cube is in solved state."""
        for face in self.faces:
            first_color = self.faces[face][0][0]
            for row in self.faces[face]:
                for sticker in row:
                    if sticker != first_color:
                        return False
        return True
    
    def __str__(self):
        """String representation of the cube state."""
        result = []
        for face in ['U', 'L', 'F', 'R', 'B', 'D']:
            result.append(f"{face} face:")
            for row in self.faces[face]:
                result.append(' '.join(row))
            result.append('')
        return '\n'.join(result)
