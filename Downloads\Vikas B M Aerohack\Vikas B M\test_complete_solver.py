"""
Complete Solver Test
Test the complete solving functionality
"""

from cube import Cube
from simple_working_solver import SimpleWorkingSolver
from kociemba_solver import KociembaSolver
import time


def test_simple_solver():
    """Test the simple working solver."""
    print("=== Testing Simple Working Solver ===")
    
    solver = SimpleWorkingSolver()
    
    # Test cases with increasing difficulty
    test_cases = [
        ("R", "Single move"),
        ("R U", "Two moves"),
        ("R U R'", "Three moves"),
        ("F R U R' U' F'", "OLL algorithm (6 moves)"),
    ]
    
    for scramble, description in test_cases:
        print(f"\n--- {description}: {scramble} ---")
        
        # Create and scramble cube
        cube = Cube(3)
        moves = scramble.split()
        for move in moves:
            cube.apply_move(move)
        
        print(f"Scrambled: {not cube.is_solved()}")
        
        # Solve
        start_time = time.time()
        solution, solve_time, success = solver.solve(cube)
        
        print(f"Solved: {success}")
        print(f"Solution length: {len(solution)} moves")
        print(f"Solve time: {solve_time:.3f} seconds")
        
        if success and len(solution) <= 15:  # Only show short solutions
            print(f"Solution: {' '.join(solution)}")
        
        if not success:
            print("❌ FAILED TO SOLVE")
            return False
    
    print("\n✅ All simple solver tests passed!")
    return True


def test_kociemba_solver():
    """Test Kociemba solver if available."""
    print("\n=== Testing Kociemba Solver ===")
    
    solver = KociembaSolver()
    if not solver.is_available():
        print("⚠️  Kociemba not available - install with: pip install kociemba")
        return True  # Not a failure, just not available
    
    # Test with simple scramble
    cube = Cube(3)
    cube.apply_move("R")
    cube.apply_move("U")
    cube.apply_move("R'")
    cube.apply_move("U'")
    
    print("Testing with R U R' U' scramble...")
    solution, solve_time, success = solver.solve(cube)
    
    print(f"Solved: {success}")
    if success:
        print(f"Solution: {' '.join(solution)} ({len(solution)} moves)")
        print(f"Solve time: {solve_time:.3f} seconds")
        print("✅ Kociemba solver working!")
    else:
        print("❌ Kociemba solver failed")
        return False
    
    return True


def test_cube_operations():
    """Test basic cube operations."""
    print("\n=== Testing Basic Cube Operations ===")
    
    # Test cube creation
    cube = Cube(3)
    if not cube.is_solved():
        print("❌ New cube is not solved")
        return False
    
    # Test basic moves
    cube.apply_move("R")
    if cube.is_solved():
        print("❌ Cube should be scrambled after R move")
        return False
    
    cube.apply_move("R'")
    if not cube.is_solved():
        print("❌ Cube should be solved after R R'")
        return False
    
    # Test scrambling
    scramble_moves = cube.scramble(5)
    if cube.is_solved():
        print("❌ Cube should be scrambled after scramble()")
        return False
    
    print(f"✅ Basic operations working - scrambled with: {' '.join(scramble_moves)}")
    return True


def test_gui_integration():
    """Test GUI integration without actually showing GUI."""
    print("\n=== Testing GUI Integration ===")
    
    try:
        from main import RubiksCubeSolverApp
        
        # Create app instance (but don't run mainloop)
        app = RubiksCubeSolverApp()
        
        # Test that components are initialized
        if not hasattr(app, 'cube'):
            print("❌ App doesn't have cube attribute")
            return False
        
        if not hasattr(app, 'simple_solver'):
            print("❌ App doesn't have simple_solver attribute")
            return False
        
        # Test cube operations through app
        app.cube.apply_move("R")
        if app.cube.is_solved():
            print("❌ App cube should be scrambled after R")
            return False
        
        app.cube.reset()
        if not app.cube.is_solved():
            print("❌ App cube should be solved after reset")
            return False
        
        print("✅ GUI integration working!")
        return True
        
    except Exception as e:
        print(f"❌ GUI integration failed: {e}")
        return False


def run_complete_test():
    """Run all tests."""
    print("🎲 COMPLETE RUBIK'S CUBE SOLVER TEST 🎲")
    print("=" * 50)
    
    all_passed = True
    
    # Test basic operations
    all_passed &= test_cube_operations()
    
    # Test simple solver
    all_passed &= test_simple_solver()
    
    # Test Kociemba solver
    all_passed &= test_kociemba_solver()
    
    # Test GUI integration
    all_passed &= test_gui_integration()
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL TESTS PASSED! 🎉")
        print("\nThe Rubik's Cube Solver is working correctly!")
        print("\nYou can now:")
        print("1. Run 'python main.py' for the full GUI application")
        print("2. Use the Simple solver for small scrambles (up to ~6 moves)")
        print("3. Install Kociemba for optimal 3x3 solutions: pip install kociemba")
        print("4. Try different cube sizes (2x2, 3x3, 4x4)")
    else:
        print("❌ SOME TESTS FAILED")
        print("Check the output above for details.")
    
    return all_passed


if __name__ == "__main__":
    run_complete_test()
