"""
Test Move Engine
Verify that the cube move engine works correctly
"""

from cube import Cube
from cube_utils import reverse_move_sequence, parse_move_sequence

def test_single_moves():
    """Test that single moves and their inverses work."""
    print("=== Testing Single Moves ===")
    
    moves_to_test = ['R', 'L', 'U', 'D', 'F', 'B']
    
    for move in moves_to_test:
        cube = Cube(3)
        
        # Apply move
        cube.apply_move(move)
        is_scrambled = not cube.is_solved()
        
        # Apply inverse
        cube.apply_move(move + "'")
        is_solved = cube.is_solved()
        
        print(f"{move}: scrambled={is_scrambled}, solved_after_inverse={is_solved}")
        
        if not is_solved:
            print(f"  ERROR: {move} + {move}' did not return to solved state!")
            return False
    
    return True

def test_double_moves():
    """Test that double moves work correctly."""
    print("\n=== Testing Double Moves ===")
    
    moves_to_test = ['R2', 'L2', 'U2', 'D2', 'F2', 'B2']
    
    for move in moves_to_test:
        cube = Cube(3)
        
        # Apply double move
        cube.apply_move(move)
        is_scrambled = not cube.is_solved()
        
        # Apply same double move again (should return to solved)
        cube.apply_move(move)
        is_solved = cube.is_solved()
        
        print(f"{move}: scrambled={is_scrambled}, solved_after_second={is_solved}")
        
        if not is_solved:
            print(f"  ERROR: {move} + {move} did not return to solved state!")
            return False
    
    return True

def test_scramble_reversal():
    """Test that scrambles can be reversed."""
    print("\n=== Testing Scramble Reversal ===")

    # These are simple scrambles that should be reversible
    test_scrambles = [
        "R",
        "R U",
        "F B",
        "L D R",
        "U2 D2",
    ]

    for scramble in test_scrambles:
        cube = Cube(3)

        # Apply scramble
        scramble_moves = parse_move_sequence(scramble)
        for move in scramble_moves:
            cube.apply_move(move)

        is_scrambled = not cube.is_solved()

        # Apply reverse
        reverse_moves = reverse_move_sequence(scramble_moves)
        for move in reverse_moves:
            cube.apply_move(move)

        is_solved = cube.is_solved()

        print(f"'{scramble}' -> '{' '.join(reverse_moves)}': scrambled={is_scrambled}, solved={is_solved}")

        if not is_solved:
            print(f"  ERROR: Scramble reversal failed!")
            print(f"  Scramble: {scramble_moves}")
            print(f"  Reverse: {reverse_moves}")
            return False

    return True

def test_known_algorithms():
    """Test known algorithms that should return to solved state."""
    print("\n=== Testing Known Algorithms ===")
    
    # These algorithms should return to solved when repeated
    algorithms = {
        "R U R' U'": 6,  # Sexy move - repeat 6 times
        "R U R' U R U2 R'": 1,  # Sune - this doesn't return to solved, it's a corner orientation
        "F R U R' U' F'": 1,  # OLL algorithm - this also doesn't return to solved
    }
    
    # Let's test simpler cases
    simple_tests = [
        ("R R'", 1),  # Should be solved after 1 repetition
        ("U U'", 1),  # Should be solved after 1 repetition  
        ("R2 R2", 1), # Should be solved after 1 repetition
        ("R U R' U'", 6),  # Sexy move repeated 6 times
    ]
    
    for algorithm, repetitions in simple_tests:
        cube = Cube(3)
        
        # Apply algorithm the specified number of times
        for _ in range(repetitions):
            moves = parse_move_sequence(algorithm)
            for move in moves:
                cube.apply_move(move)
        
        is_solved = cube.is_solved()
        print(f"'{algorithm}' x{repetitions}: solved={is_solved}")
        
        if algorithm in ["R R'", "U U'", "R2 R2"] and not is_solved:
            print(f"  ERROR: Basic inverse failed!")
            return False
    
    return True

def main():
    """Run all tests."""
    print("🧪 TESTING CUBE MOVE ENGINE 🧪")
    print("=" * 40)
    
    all_passed = True
    
    all_passed &= test_single_moves()
    all_passed &= test_double_moves() 
    all_passed &= test_scramble_reversal()
    all_passed &= test_known_algorithms()
    
    print("\n" + "=" * 40)
    if all_passed:
        print("✅ ALL TESTS PASSED - Move engine is working correctly!")
    else:
        print("❌ SOME TESTS FAILED - Move engine has issues!")
    
    return all_passed

if __name__ == "__main__":
    main()
