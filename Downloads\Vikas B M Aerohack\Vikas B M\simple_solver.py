"""
Simple Brute Force Solver
A basic solver that uses brute force search for small scrambles
"""

from cube import Cube
from cube_utils import parse_move_sequence
import random
from collections import deque


class SimpleSolver:
    """
    Simple brute force solver for demonstration.
    Works well for small scrambles (up to 6-8 moves).
    """
    
    def __init__(self):
        self.basic_moves = ['R', "R'", 'L', "L'", 'U', "U'", 'D', "D'", 'F', "F'", 'B', "B'"]
        self.max_depth = 12
    
    def solve(self, cube):
        """
        Solve cube using breadth-first search.
        Returns (moves, solve_time, success)
        """
        import time
        start_time = time.time()
        
        if cube.is_solved():
            return [], time.time() - start_time, True
        
        # BFS to find solution
        queue = deque([(cube.copy(), [])])
        visited = set()
        
        for depth in range(1, self.max_depth + 1):
            print(f"Searching depth {depth}...")
            found, solution = self._search_depth(cube, depth)
            if found:
                solve_time = time.time() - start_time
                return solution, solve_time, True
        
        # If no solution found, try random moves
        return self._random_solve(cube, start_time)
    
    def _search_depth(self, original_cube, max_depth):
        """Search for solution at specific depth."""
        def dfs(cube, moves, depth):
            if cube.is_solved():
                return True, moves
            
            if depth >= max_depth:
                return False, []
            
            for move in self.basic_moves:
                # Avoid redundant moves
                if moves and self._is_redundant(moves[-1], move):
                    continue
                
                test_cube = cube.copy()
                test_cube.apply_move(move)
                
                found, solution = dfs(test_cube, moves + [move], depth + 1)
                if found:
                    return True, solution
            
            return False, []
        
        return dfs(original_cube.copy(), [], 0)
    
    def _is_redundant(self, last_move, current_move):
        """Check if current move is redundant after last move."""
        # Same face moves
        if last_move[0] == current_move[0]:
            return True
        
        # Opposite face moves that cancel out
        opposites = {'R': 'L', 'L': 'R', 'U': 'D', 'D': 'U', 'F': 'B', 'B': 'F'}
        if last_move[0] in opposites and opposites[last_move[0]] == current_move[0]:
            return True
        
        return False
    
    def _random_solve(self, cube, start_time):
        """Fallback random solving approach."""
        print("Using random solving approach...")
        
        best_distance = float('inf')
        best_moves = []
        
        for attempt in range(100):  # Try 100 random sequences
            test_cube = cube.copy()
            moves = []
            
            for _ in range(20):  # Try up to 20 random moves
                move = random.choice(self.basic_moves)
                test_cube.apply_move(move)
                moves.append(move)
                
                if test_cube.is_solved():
                    solve_time = time.time() - start_time
                    return moves, solve_time, True
                
                # Track best attempt
                distance = self._cube_distance(test_cube)
                if distance < best_distance:
                    best_distance = distance
                    best_moves = moves.copy()
        
        # Return best attempt even if not solved
        solve_time = time.time() - start_time
        return best_moves, solve_time, False
    
    def _cube_distance(self, cube):
        """Calculate how far cube is from solved state."""
        solved_cube = Cube(cube.size)
        distance = 0
        
        for face in cube.faces:
            for i in range(cube.size):
                for j in range(cube.size):
                    if cube.faces[face][i][j] != solved_cube.faces[face][i][j]:
                        distance += 1
        
        return distance


class PatternSolver:
    """
    Solver that uses known patterns and algorithms.
    More reliable than brute force for larger scrambles.
    """
    
    def __init__(self):
        # Common algorithms that help solve cubes
        self.algorithms = [
            "R U R' U'",           # Sexy move
            "R U R' U R U2 R'",    # Sune
            "R U2 R' U' R U' R'",  # Anti-Sune
            "F R U R' U' F'",      # F2L insert
            "R U R' F' R U R' U' R' F R2 U' R'",  # T-perm
            "R U R' U R U2 R'",    # Right trigger
            "L' U' L U' L' U2 L",  # Left trigger
        ]
    
    def solve(self, cube):
        """
        Solve using pattern recognition and algorithms.
        """
        import time
        start_time = time.time()
        
        if cube.is_solved():
            return [], time.time() - start_time, True
        
        all_moves = []
        max_iterations = 50
        
        for iteration in range(max_iterations):
            if cube.is_solved():
                solve_time = time.time() - start_time
                return all_moves, solve_time, True
            
            # Try each algorithm
            best_improvement = 0
            best_algorithm = None
            best_setup = []
            
            for algorithm in self.algorithms:
                for setup_moves in [[], ['U'], ['U2'], ["U'"]]:  # Try different setups
                    test_cube = cube.copy()
                    
                    # Apply setup
                    for move in setup_moves:
                        test_cube.apply_move(move)
                    
                    # Apply algorithm
                    alg_moves = parse_move_sequence(algorithm)
                    for move in alg_moves:
                        test_cube.apply_move(move)
                    
                    # Check improvement
                    improvement = self._calculate_improvement(cube, test_cube)
                    if improvement > best_improvement:
                        best_improvement = improvement
                        best_algorithm = algorithm
                        best_setup = setup_moves
            
            # Apply best found sequence
            if best_algorithm:
                # Apply setup
                for move in best_setup:
                    cube.apply_move(move)
                    all_moves.append(move)
                
                # Apply algorithm
                alg_moves = parse_move_sequence(best_algorithm)
                for move in alg_moves:
                    cube.apply_move(move)
                    all_moves.append(move)
            else:
                # No improvement found, try random move
                random_move = random.choice(['R', "R'", 'U', "U'", 'F', "F'"])
                cube.apply_move(random_move)
                all_moves.append(random_move)
        
        solve_time = time.time() - start_time
        return all_moves, solve_time, cube.is_solved()
    
    def _calculate_improvement(self, original_cube, new_cube):
        """Calculate how much the cube state improved."""
        original_distance = self._cube_distance(original_cube)
        new_distance = self._cube_distance(new_cube)
        return original_distance - new_distance
    
    def _cube_distance(self, cube):
        """Calculate distance from solved state."""
        solved_cube = Cube(cube.size)
        distance = 0
        
        for face in cube.faces:
            for i in range(cube.size):
                for j in range(cube.size):
                    if cube.faces[face][i][j] != solved_cube.faces[face][i][j]:
                        distance += 1
        
        return distance


def create_working_solver():
    """Create a solver that actually works for demonstration."""
    return PatternSolver()


def demo_simple_solver():
    """Demonstrate the simple solver."""
    print("=== Simple Solver Demo ===")
    
    # Test with very simple scramble
    cube = Cube(3)
    print("Original state:", cube.is_solved())
    
    # Apply simple scramble
    simple_scramble = ["R", "U"]
    for move in simple_scramble:
        cube.apply_move(move)
    print(f"Applied scramble: {' '.join(simple_scramble)}")
    print("Scrambled state:", cube.is_solved())
    
    # Try to solve
    solver = PatternSolver()
    solution, solve_time, success = solver.solve(cube)
    
    print(f"Solve attempt: {'Success' if success else 'Failed'}")
    print(f"Solution length: {len(solution)} moves")
    print(f"Solve time: {solve_time:.3f} seconds")
    if len(solution) <= 20:  # Only show short solutions
        print(f"Solution: {' '.join(solution)}")
    
    return success


if __name__ == "__main__":
    demo_simple_solver()
