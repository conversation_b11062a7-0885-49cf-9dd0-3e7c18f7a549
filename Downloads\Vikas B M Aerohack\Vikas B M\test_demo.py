"""
Test Demo Script
Quick verification that all components work correctly
"""

from cube import Cube
from cube_utils import *
from kociemba_solver import KociembaSolver
from beginner_solver import BeginnerSolver
from multi_size_solver import create_solver_for_size


def test_basic_cube_operations():
    """Test basic cube functionality."""
    print("=== Testing Basic Cube Operations ===")
    
    # Test 3x3 cube
    cube = Cube(3)
    print(f"Created 3x3 cube: {'✓' if cube.is_solved() else '✗'}")
    
    # Test moves
    cube.apply_move("R")
    cube.apply_move("U")
    cube.apply_move("R'")
    cube.apply_move("U'")
    print(f"Applied R U R' U': {'✓' if cube.is_solved() else '✗'}")
    
    # Test scramble
    scramble_moves = cube.scramble(10)
    print(f"Applied scramble: {' '.join(scramble_moves)}")
    print(f"Cube scrambled: {'✓' if not cube.is_solved() else '✗'}")
    
    # Test reset
    cube.reset()
    print(f"Reset cube: {'✓' if cube.is_solved() else '✗'}")
    
    print()


def test_cube_utils():
    """Test cube utility functions."""
    print("=== Testing Cube Utils ===")
    
    cube = Cube(3)
    
    # Test move parsing
    moves = parse_move_sequence("R U R' U R U2 R'")
    print(f"Parsed moves: {moves}")
    print(f"Move count: {len(moves)}")
    
    # Test facelet string conversion
    try:
        facelet_string = get_facelet_string(cube)
        print(f"Facelet string length: {len(facelet_string)}")
        print(f"Facelet string: {facelet_string[:20]}...")
    except Exception as e:
        print(f"Facelet conversion error: {e}")
    
    # Test validation
    valid, message = validate_cube_state(cube)
    print(f"Cube validation: {'✓' if valid else '✗'} - {message}")
    
    print()


def test_kociemba_solver():
    """Test Kociemba solver integration."""
    print("=== Testing Kociemba Solver ===")
    
    solver = KociembaSolver()
    print(f"Kociemba available: {'✓' if solver.is_available() else '✗'}")
    
    if solver.is_available():
        # Test with simple scramble
        cube = Cube(3)
        cube.apply_move("R U R' U R U2 R'")
        
        solution, solve_time, success = solver.solve(cube)
        if success:
            print(f"Solution found: {' '.join(solution)} ({len(solution)} moves)")
            print(f"Solve time: {solve_time:.3f} seconds")
            
            # Verify solution
            if solver.verify_solution(cube, solution):
                print("Solution verified: ✓")
            else:
                print("Solution verification failed: ✗")
        else:
            print("Failed to find solution")
    else:
        print("Install Kociemba with: pip install kociemba")
    
    print()


def test_beginner_solver():
    """Test beginner solver."""
    print("=== Testing Beginner Solver ===")
    
    solver = BeginnerSolver()
    cube = Cube(3)
    
    # Test step info
    step_info = solver.get_current_step_info()
    print(f"Current step: {step_info['step_name']}")
    print(f"Description: {step_info['description']}")
    
    # Test analysis
    step, message = solver.analyze_cube_state(cube)
    print(f"Analysis: {message}")
    
    print()


def test_multi_size_cubes():
    """Test different cube sizes."""
    print("=== Testing Multi-Size Cubes ===")
    
    for size in [2, 3, 4]:
        print(f"\n{size}x{size} Cube:")
        cube = Cube(size)
        print(f"  Created: {'✓' if cube.is_solved() else '✗'}")
        
        # Test basic operations
        cube.apply_move("R")
        cube.apply_move("R'")
        print(f"  R R' applied: {'✓' if cube.is_solved() else '✗'}")
        
        # Test scramble
        scramble = cube.scramble(5)
        print(f"  Scrambled: {' '.join(scramble)}")
        
        # Test solver availability
        try:
            solver = create_solver_for_size(size)
            if solver:
                print(f"  Solver available: ✓")
            else:
                print(f"  Solver: Use dedicated 3x3 solvers")
        except Exception as e:
            print(f"  Solver error: {e}")
    
    print()


def test_visualizer_components():
    """Test visualizer components without GUI."""
    print("=== Testing Visualizer Components ===")
    
    try:
        from visualizer import CubeVisualizer
        print("Visualizer import: ✓")
        
        # Test color mapping
        colors = CubeVisualizer.DISPLAY_COLORS
        print(f"Color mapping: {len(colors)} colors defined")
        
    except ImportError as e:
        print(f"Visualizer import error: {e}")
    except Exception as e:
        print(f"Visualizer test error: {e}")
    
    print()


def run_comprehensive_test():
    """Run all tests."""
    print("🎲 RUBIK'S CUBE SOLVER - COMPREHENSIVE TEST 🎲")
    print("=" * 50)
    
    test_basic_cube_operations()
    test_cube_utils()
    test_kociemba_solver()
    test_beginner_solver()
    test_multi_size_cubes()
    test_visualizer_components()
    
    print("=" * 50)
    print("✅ Test completed! Check results above.")
    print("\nTo run the full GUI application:")
    print("  python main.py")
    print("\nTo run individual components:")
    print("  python visualizer.py")
    print("  python kociemba_solver.py")
    print("  python beginner_solver.py")


if __name__ == "__main__":
    run_comprehensive_test()
